from django.db import models
from django.contrib.auth.models import User
from django.utils.timezone import now


class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    points = models.IntegerField(default=5)  # Default points for new users

class Music(models.Model):
    hash = models.CharField(max_length=255,primary_key=True,db_index=True)
    title = models.CharField(max_length=255)
    artist = models.CharField(max_length=255)
    album = models.Char<PERSON>ield(max_length=255)
    created = models.DateTimeField(auto_now_add=True)
    could_download = models.BooleanField(default=False)
    download_count = models.IntegerField(default=0)  # Total number of downloads for this music
    partition = models.IntegerField(default=0)  # Storage partition for the music file

class UserDownloadedMusic(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='downloaded_music')
    music = models.ForeignKey(Music, on_delete=models.CASCADE)
    downloaded_at = models.DateTimeField(auto_now_add=True)
    class Meta:
        indexes = [
            models.Index(fields=['user', 'downloaded_at']),  # 复合索引，加快查询
        ]

class WebdavConfig(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='webdav_config',db_index=True)
    url = models.URLField(max_length=255)
    username = models.CharField(max_length=150)
    password = models.CharField(max_length=128)
    signature = models.CharField(max_length=64, blank=True, null=True)  # 预生成的签名
    is_tested = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class UploadTask(models.Model):
    STATUS_CHOICES = [
        ('processing', 'Processing'),
        ('success', 'Success'),
        ('failed', 'Failed')
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='upload_tasks',db_index=True)
    music = models.ForeignKey(Music, on_delete=models.CASCADE, related_name='upload_tasks')
    webdav_config = models.ForeignKey(WebdavConfig, on_delete=models.CASCADE, related_name='upload_tasks', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='processing')
    reason = models.TextField(blank=True, null=True)
    format_type = models.CharField(max_length=10, default='mp3')  # 目标格式
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

class RedeemCode(models.Model):
    code = models.CharField(max_length=128, unique=True)
    points = models.IntegerField()
    expiration_date = models.DateTimeField()

    def is_expired(self):
        return self.expiration_date < now()  # 使用 Django 的 timezone.now()

    def is_redeemed(self):
        return RedemptionRecord.objects.filter(redeem_code=self).exists()

    def __str__(self):
        return f"{self.code} - {self.points} points"

class RedemptionRecord(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    redeem_code = models.OneToOneField(RedeemCode, on_delete=models.CASCADE)  # 确保一个兑换码只能兑换一次
    redeemed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} redeemed {self.redeem_code.code}"

class Product(models.Model):
    name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)  # 商品价格
    points = models.IntegerField()  # 兑换的积分值

class Order(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    out_trade_no = models.CharField(max_length=64, unique=True)  # 商户订单号
    trade_no = models.CharField(max_length=64, blank=True, null=True)  # 第三方支付订单号
    amount = models.DecimalField(max_digits=10, decimal_places=2)  # 充值金额
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')  # 订单状态
    created_at = models.DateTimeField(auto_now_add=True)
